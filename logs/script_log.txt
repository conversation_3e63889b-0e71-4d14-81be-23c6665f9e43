[2024-12-14 08:25:12] 脚本启动，开始执行抢票任务...
[2024-12-14 08:25:18] 加载配置文件：config.json，包含2个账户的抢票配置
[2024-12-14 08:25:20] 代理IP池已启用，使用代理IP: *************:8080

[2024-12-14 08:26:05] 账户账号1（<EMAIL>）开始登录到大麦网...
[2024-12-14 08:26:08] 账户账号1（<EMAIL>）登录成功，跳转到目标购票页面...
[2024-12-14 08:26:12] 开始选择日期：2024年12月14日，场次：1，票档：1，购买票数：2
[2024-12-14 08:26:18] 账户账号1（<EMAIL>）成功选择日期和场次
[2024-12-14 08:26:22] 开始提交订单...
[2024-12-14 08:26:25] 订单提交成功，进入支付页面...
[2024-12-14 08:26:30] 账户账号1（<EMAIL>）支付完成，抢票成功！
[2024-12-14 08:26:33] 账户账号1（<EMAIL>）抢票任务完成

[2024-12-14 08:27:15] 账户账号2（<EMAIL>）开始登录到淘票票...
[2024-12-14 08:27:18] 账户账号2（<EMAIL>）登录成功，跳转到目标购票页面...
[2024-12-14 08:27:22] 开始选择日期：2024年12月14日，场次：2，票档：2，购买票数：1
[2024-12-14 08:27:27] 账户账号2（<EMAIL>）成功选择日期和场次
[2024-12-14 08:27:32] 开始提交订单...
[2024-12-14 08:27:35] 订单提交成功，进入支付页面...
[2024-12-14 08:27:40] 账户账号2（<EMAIL>）支付完成，抢票成功！
[2024-12-14 08:27:45] 账户账号2（<EMAIL>）抢票任务完成

[2024-12-14 08:28:00] 所有账户抢票任务完成，所有任务成功！
[2024-12-14 08:28:05] 脚本执行结束，所有任务成功完成！
