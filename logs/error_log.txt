[2024-12-14 08:10:01] 脚本启动，开始执行抢票任务...

[2024-12-14 08:10:10] 加载配置文件：config.json，包含2个账户的抢票配置
[2024-12-14 08:10:12] 代理IP池已启用，使用代理IP: *************:8080

[2024-12-14 08:11:05] 账户账号1（<EMAIL>）开始登录到大麦网...
[2024-12-14 08:11:08] 账户账号1（<EMAIL>）登录失败，错误代码：401（未授权访问）
[2024-12-14 08:11:10] 错误：登录失败，检查账户密码或网络连接

[2024-12-14 08:11:20] 账户账号1（<EMAIL>）开始重新登录，尝试获取验证码...
[2024-12-14 08:11:25] 验证码获取失败，网络连接中断，请检查代理设置
[2024-12-14 08:11:30] 错误：验证码获取失败，尝试切换代理IP...
[2024-12-14 08:11:35] 成功切换代理IP到：*************:8080

[2024-12-14 08:12:00] 账户账号1（<EMAIL>）成功登录，跳转到目标购票页面
[2024-12-14 08:12:10] 账户账号1（<EMAIL>）开始选择日期：2024年12月14日，场次：1，票档：1，购买票数：2

[2024-12-14 08:12:30] 账户账号1（<EMAIL>）选择日期和场次失败，错误：场次已售罄
[2024-12-14 08:12:35] 错误：场次已售罄，尝试选择下一个场次

[2024-12-14 08:12:40] 账户账号1（<EMAIL>）开始选择新的场次：2
[2024-12-14 08:12:45] 成功选择场次2，继续提交订单...

[2024-12-14 08:13:00] 账户账号1（<EMAIL>）订单提交失败，错误：支付页面加载超时
[2024-12-14 08:13:05] 错误：支付页面加载超时，重试提交订单...

[2024-12-14 08:13:20] 账户账号1（<EMAIL>）重新提交订单...
[2024-12-14 08:13:30] 账户账号1（<EMAIL>）支付完成，抢票成功！

[2024-12-14 08:14:00] 账户账号2（<EMAIL>）开始登录到淘票票...
[2024-12-14 08:14:05] 账户账号2（<EMAIL>）登录失败，错误代码：504（网关超时）
[2024-12-14 08:14:10] 错误：网关超时，重新连接...
[2024-12-14 08:14:15] 成功重新连接，继续登录...

[2024-12-14 08:14:25] 账户账号2（<EMAIL>）登录成功，跳转到目标购票页面
[2024-12-14 08:14:35] 账户账号2（<EMAIL>）开始选择日期：2024年12月14日，场次：2，票档：3，购买票数：1

[2024-12-14 08:14:50] 账户账号2（<EMAIL>）选择日期和场次失败，错误：票档已售罄
[2024-12-14 08:14:55] 错误：票档已售罄，尝试选择其他票档

[2024-12-14 08:15:00] 账户账号2（<EMAIL>）开始选择票档：1
[2024-12-14 08:15:05] 成功选择票档1，继续提交订单...

[2024-12-14 08:15:15] 账户账号2（<EMAIL>）订单提交失败，错误：库存不足
[2024-12-14 08:15:20] 错误：库存不足，稍后重试...

[2024-12-14 08:15:30] 账户账号2（<EMAIL>）重试提交订单...
[2024-12-14 08:15:35] 订单提交成功，进入支付页面...
[2024-12-14 08:15:40] 账户账号2（<EMAIL>）支付完成，抢票成功！

[2024-12-14 08:16:00] 所有账户抢票任务完成，所有任务成功！
[2024-12-14 08:16:05] 脚本执行结束，所有任务成功完成！
