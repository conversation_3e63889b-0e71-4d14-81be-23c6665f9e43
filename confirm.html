<div id="confirm" class="loading-container" style="background: rgba(0, 0, 0, 0.5);">
    <div
        style="width: 1408px; border-radius: 25.6px; background: rgb(255, 255, 255); display: flex; -webkit-box-orient: vertical; flex-direction: column; -webkit-box-pack: start; justify-content: flex-start; -webkit-box-align: center; align-items: center;">
        <div
            style="width: 100%; border-bottom: 1px solid rgb(245, 245, 245); display: flex; -webkit-box-pack: center; justify-content: center; -webkit-box-align: center; align-items: center;">
            <div id="confirmContent" style="padding: 76.8px;">对不起，您选购的商品库存不足，请重新选购</div>
        </div>
        <div style="width: 100%; height: 256px; display: flex; -webkit-box-orient: horizontal; flex-direction: row; -webkit-box-pack: justify; justify-content: space-between; -webkit-box-align: center; align-items: center;"
            data-spm-anchor-id="a2o71.orderconfirm.0.i2.6e435890s11c4z">
            <div
                style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; -webkit-box-pack: center; justify-content: center; -webkit-box-align: center; align-items: center; border-right: 1px solid rgb(245, 245, 245);">
                取消</div>
            <div
                style="-webkit-box-flex: 1; flex: 1 1 0%; display: flex; -webkit-box-pack: center; justify-content: center; -webkit-box-align: center; align-items: center; color: rgb(255, 45, 121);">
                返回</div>
        </div>
    </div>
</div>
